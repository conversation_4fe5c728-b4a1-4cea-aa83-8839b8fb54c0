Excellent. Using a checklist is the perfect way to approach a structured refactor. This checklist is designed to guide you (or an AI assistant) through implementing the UI/UX redesign I proposed. It breaks the process into logical phases, referencing your existing components and suggesting new ones.

---

### Code Refactor Checklist: UI/UX Redesign Implementation

This checklist will transform your application from a tab-based layout to a more modern, workflow-centric design with a sidebar and global timer bar.

#### **Phase 0: Project Setup**
*   [ ] **Create a New Git Branch:** Start on a fresh branch to isolate these significant changes. (`git checkout -b feature/ui-redesign`)
*   [ ] **Review Dependencies:** Ensure you have an icon library installed (`@mui/icons-material` is already in your `package.json`, which is great).

---

#### **Phase 1: Implement the New Global Layout & Navigation**
The goal here is to create the new application shell. This will temporarily break the app's functionality, which is expected.

*   [x] **Create `Sidebar.tsx` Component:**
    *   Create a new file: `src/components/layout/Sidebar.tsx`.
    *   Use MUI `Drawer` (permanent variant), `List`, and `ListItemButton` components.
    *   Add navigation items with icons for:
        *   Dashboard (`DashboardIcon`)
        *   Tasks (`TaskIcon`)
        *   Reports (`BarChartIcon`)
        *   Settings (`SettingsIcon`)
    *   Manage the selected state of the navigation items.

*   [ ] **Refactor `App.tsx` for the New Layout:**
    *   Remove the `Navbar` component and its related state (`currentTab`, `handleTabChange`).
    *   Change the main layout to a `Box` with `display: 'flex'`.
    *   Add the new `Sidebar` component to the left.
    *   Create a main content area `Box` that will render the active page.
    *   Implement state management to track the currently selected page (e.g., `const [activeView, setActiveView] = useState('dashboard')`).
    *   Conditionally render placeholder `Typography` for each view (Dashboard, Tasks, Reports, Settings) in the main content area based on `activeView`.

*   [ ] **Create the `GlobalTimerBar.tsx` Component:**
    *   Create a new file: `src/components/layout/GlobalTimerBar.tsx`.
    *   This component will now contain the primary timer logic previously in `TimeEntryForm.tsx`.
    *   **State 1 (Timer Stopped):** Display a `TaskSelector`-like input field and a "Start" button.
    *   **State 2 (Timer Running):** Display the current task name, the running `TimerDisplay`, and a "Stop" button.
    *   This component will need props for `activeEntry`, `onStart`, `onStop`, `predefinedTasks`, etc.

*   [ ] **Integrate `GlobalTimerBar.tsx` into `App.tsx`:**
    *   Add the `GlobalTimerBar` at the top of the main `Box` in `App.tsx`, above the sidebar/content flex container.
    *   Pass the necessary state (`activeEntry`, `tasks`, etc.) and handlers from `App.tsx`.

---

#### **Phase 2: Build the New Pages**

##### **Dashboard Page**
*   [ ] **Create `DashboardPage.tsx`:**
    *   Create a new page component file: `src/components/pages/DashboardPage.tsx`.
    *   Update `App.tsx` to render this component when the "Dashboard" view is active.
*   [ ] **Implement Dashboard Widgets:**
    *   Add a row of `StatsCard` components at the top.
    *   Create logic/hooks to calculate "Total Time Today," "Earnings Today," and "Tasks Worked On."
*   [ ] **Create `TodaysEntriesList.tsx`:**
    *   Create a new component that filters `timeEntries` for the current day.
    *   Use the existing `TimeEntryRow` component to display the entries in a simple table or list.
    *   Add this component to the `DashboardPage`.

##### **Tasks Page (Master-Detail)**
*   [ ] **Create `TasksPage.tsx`:**
    *   Create a new page component file: `src/components/pages/TasksPage.tsx`.
    *   Update `App.tsx` to render it when the "Tasks" view is active.
*   [ ] **Implement Two-Pane Layout:**
    *   Use a `Box` with `display: 'flex'` to create a two-pane view.
*   [ ] **Refactor `TaskManagement.tsx` into a `TaskListPane.tsx`:**
    *   Create `TaskListPane.tsx` which will serve as the master list on the left.
    *   Adapt the table from `TaskManagement.tsx` into a selectable list (or keep the table). Use `VirtualizedList` if the task list can be long.
    *   Add search and filter controls above the list.
    *   It should take an `onSelectTask` prop and call it when a task is clicked.
*   [ ] **Integrate `TaskDetail.tsx`:**
    *   The right pane of `TasksPage.tsx` will render `TaskDetail.tsx`.
    *   Pass the currently selected task from `TasksPage.tsx` state down to `TaskDetail.tsx`.
*   [ ] **Enhance `TaskDetail.tsx`:**
    *   Add a new section to `TaskDetail.tsx` to display a filtered list of all time entries associated with the selected task.

##### **Reports Page**
*   [ ] **Create `ReportsPage.tsx`:**
    *   Create a new page component file: `src/components/pages/ReportsPage.tsx`.
    *   Update `App.tsx` to render it when the "Reports" view is active.
*   [ ] **Evolve `CalendarView.tsx` into the Reports Page:**
    *   Move the core table logic from `CalendarView.tsx` into `ReportsPage.tsx`.
    *   Replace the simple `DateNavigator` with a more powerful date range picker (e.g., from MUI X or `date-fns`).
    *   Add a multi-select `Autocomplete` component to filter by one or more tasks.
*   [ ] **(Optional) Add Visualizations:**
    *   Integrate a charting library (like Recharts or Chart.js).
    *   Add components for a bar chart (time per day) and a pie chart (time per task).

##### **Settings Page**
*   [ ] **Create `SettingsPage.tsx`:**
    *   Create a new page component file: `src/components/pages/SettingsPage.tsx`.
    *   Update `App.tsx` to render it when the "Settings" view is active.
*   [ ] **Move `NoteTemplates` Functionality:**
    *   Create a "Note Templates" section within `SettingsPage.tsx`.
    *   Render the entire `TemplateBuilder` component inside this section.
*   [ ] **Create Data Management Section:**
    *   Add a section for "Data Management."
    *   Add buttons to trigger the `exportData` functions from your `useDataBackup` hook.

---

#### **Phase 3: Cleanup and Final Polish**
*   [ ] **Deprecate Old Components:**
    *   Delete `src/components/layout/Navbar.tsx`.
    *   Delete `src/components/pages/TimeEntryForm.tsx` (its logic is now in `GlobalTimerBar`). You may keep a version for a "manual entry" modal if desired.
    *   Delete `src/components/pages/NoteTemplates.tsx`.
    *   Delete `src/components/pages/CalendarView.tsx` (its logic is now in `ReportsPage`).

*   [ ] **Refactor State Management in `App.tsx`:**
    *   Review all state and props being passed. Ensure they align with the new component structure.
    *   The logic for starting/stopping the timer will now primarily interact with the `GlobalTimerBar`.

*   [ ] **Update `useSystemTray.ts`:**
    *   The `onShowNewTaskDialog` handler should now open a modal dialog instead of relying on a dedicated page component.
    *   Ensure tray actions correctly interact with the new `GlobalTimerBar` state.

*   [ ] **Implement Empty States:**
    *   Add helpful messages and "call to action" buttons for when the Dashboard, Task List, or Reports are empty.

*   [ ] **Review and Test:**
    *   Thoroughly test all workflows: starting a timer, switching pages while a timer is running, creating a task, adding notes, viewing reports.
    *   Check for console errors and warnings.

---

### **Pro Tip for Execution**

When using an AI assistant to augment your code, feed it one checklist item at a time. This micro-task approach yields better, more accurate results.

**Example Prompt:**
> "Here is my `App.tsx` file content: `[...paste code...]`. Based on the refactor plan, I need to remove the top `Navbar` and replace it with a new layout using a `Sidebar` component on the left and a main content area. Create a placeholder `Sidebar` component and show me the modified `App.tsx`."
